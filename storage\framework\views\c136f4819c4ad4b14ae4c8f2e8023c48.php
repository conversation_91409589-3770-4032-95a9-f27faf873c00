<?php $__env->startSection('title', 'Applications'); ?>

<?php $__env->startSection('breadcrumbs'); ?>
    <?php if (isset($component)) { $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumb','data' => ['items' => [['title' => 'Applications', 'icon' => 'fas fa-graduation-cap']]]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['items' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([['title' => 'Applications', 'icon' => 'fas fa-graduation-cap']])]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $attributes = $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $component = $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="dashboard-header">
        <h1>Scholarship Applications</h1>
        <div class="filter-controls">
            <form action="<?php echo e(route('admin.applications')); ?>" method="GET">
                <select name="status" class="filter-select" onchange="this.form.submit()">
                    <option value="">All Statuses</option>
                    <option value="Pending Review" <?php echo e($currentStatus == 'Pending Review' ? 'selected' : ''); ?>>
                        Pending Review</option>
                    <option value="Under Committee Review"
                        <?php echo e($currentStatus == 'Under Committee Review' ? 'selected' : ''); ?>>Committee Review
                    </option>
                </select>
                <select name="type" class="filter-select" onchange="this.form.submit()">
                    <option value="">All Types</option>
                    <option value="government" <?php echo e($currentType == 'government' ? 'selected' : ''); ?>>Government Scholarship
                    </option>
                    <option value="presidents" <?php echo e($currentType == 'presidents' ? 'selected' : ''); ?>>President's
                        Scholarship</option>
                    <option value="employees" <?php echo e($currentType == 'employees' ? 'selected' : ''); ?>>Employees
                        Scholar</option>
                    <option value="private" <?php echo e($currentType == 'private' ? 'selected' : ''); ?>>Private
                        Scholarship</option>
                </select>
            </form>
        </div>
    </div>



    <div class="table-container">
        <table class="applications-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Student Name</th>
                    <th>Student ID</th>
                    <th>Scholarship Type</th>
                    <th>Date Applied</th>
                    <th>Status</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                <?php $__currentLoopData = $applications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $application): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td><?php echo e($application->application_id); ?></td>
                        <td><?php echo e($application->first_name); ?> <?php echo e($application->last_name); ?></td>
                        <td><?php echo e($application->student_id); ?></td>
                        <td>
                            <?php if($application->scholarship_type == 'government'): ?>
                                Government Scholarship
                                <?php if($application->government_benefactor_type): ?>
                                    <br><small class="text-muted">(<?php echo e($application->government_benefactor_type); ?>)</small>
                                <?php endif; ?>
                            <?php elseif($application->scholarship_type == 'presidents'): ?>
                                President's Scholarship
                            <?php elseif($application->scholarship_type == 'employees'): ?>
                                Employees Scholar
                            <?php elseif($application->scholarship_type == 'private'): ?>
                                Private Scholarship
                            <?php else: ?>
                                <?php echo e(ucfirst($application->scholarship_type)); ?>

                            <?php endif; ?>
                        </td>
                        <td><?php echo e($application->created_at->format('M d, Y')); ?></td>
                        <td>
                            <?php if($application->status == 'Pending Review'): ?>
                                <span class="status pending">Pending Review</span>
                            <?php elseif($application->status == 'Under Committee Review'): ?>
                                <span class="status review">Committee Review</span>
                            <?php elseif($application->status == 'Approved'): ?>
                                <span class="status approved">Approved</span>
                            <?php elseif($application->status == 'Rejected'): ?>
                                <span class="status rejected">Rejected</span>
                            <?php else: ?>
                                <span class="status"><?php echo e($application->status); ?></span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <a href="<?php echo e(route('admin.application.view', $application->application_id)); ?>"
                                class="action-btn">View</a>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>


<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        // Application management functions can be added here
        console.log('Applications page loaded');
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive - St. Paul University Philippines\Desktop\CAPSTONE\System\Thesis\resources\views/admin/applications.blade.php ENDPATH**/ ?>